#!/bin/bash

# MongoDB Setup Script for Ubuntu VPS
# This script will help you install and configure MongoDB properly

echo "=== MongoDB Setup Script for Ubuntu VPS ==="
echo ""

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   echo "This script should not be run as root for security reasons."
   echo "Please run as a regular user with sudo privileges."
   exit 1
fi

# Function to check if MongoDB is already installed
check_mongodb_installed() {
    if command -v mongod &> /dev/null; then
        echo "✅ MongoDB is already installed"
        mongod --version | head -1
        return 0
    else
        echo "❌ MongoDB is not installed"
        return 1
    fi
}

# Function to install MongoDB
install_mongodb() {
    echo ""
    echo "--- Installing MongoDB ---"
    
    # Import the public key used by the package management system
    echo "Adding MongoDB GPG key..."
    curl -fsSL https://www.mongodb.org/static/pgp/server-7.0.asc | sudo gpg --dearmor -o /usr/share/keyrings/mongodb-server-7.0.gpg
    
    # Create a list file for MongoDB
    echo "Adding MongoDB repository..."
    echo "deb [ arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-server-7.0.gpg ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/7.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-7.0.list
    
    # Reload local package database
    echo "Updating package database..."
    sudo apt-get update
    
    # Install MongoDB packages
    echo "Installing MongoDB..."
    sudo apt-get install -y mongodb-org
    
    if [ $? -eq 0 ]; then
        echo "✅ MongoDB installed successfully"
    else
        echo "❌ MongoDB installation failed"
        exit 1
    fi
}

# Function to configure MongoDB for external connections
configure_mongodb() {
    echo ""
    echo "--- Configuring MongoDB ---"
    
    # Backup original config
    sudo cp /etc/mongod.conf /etc/mongod.conf.backup
    echo "✅ Backed up original config to /etc/mongod.conf.backup"
    
    # Configure MongoDB to accept external connections
    echo "Configuring MongoDB to accept external connections..."
    
    # Update bind IP to allow external connections
    sudo sed -i 's/bindIp: 127.0.0.1/bindIp: 0.0.0.0/' /etc/mongod.conf
    
    # Ensure the port is set correctly
    if ! grep -q "port: 27017" /etc/mongod.conf; then
        sudo sed -i '/net:/a\  port: 27017' /etc/mongod.conf
    fi
    
    echo "✅ MongoDB configuration updated"
    echo "MongoDB will now accept connections from any IP address"
    echo "⚠️  WARNING: This is less secure. Consider restricting to specific IPs in production"
}

# Function to start and enable MongoDB service
start_mongodb() {
    echo ""
    echo "--- Starting MongoDB Service ---"
    
    # Start MongoDB
    sudo systemctl start mongod
    
    if [ $? -eq 0 ]; then
        echo "✅ MongoDB service started"
    else
        echo "❌ Failed to start MongoDB service"
        echo "Checking service status..."
        sudo systemctl status mongod
        return 1
    fi
    
    # Enable MongoDB to start on boot
    sudo systemctl enable mongod
    echo "✅ MongoDB service enabled for auto-start on boot"
    
    # Check service status
    echo ""
    echo "MongoDB service status:"
    sudo systemctl status mongod --no-pager -l
}

# Function to configure firewall
configure_firewall() {
    echo ""
    echo "--- Configuring Firewall ---"
    
    # Check if ufw is active
    if sudo ufw status | grep -q "Status: active"; then
        echo "UFW firewall is active. Adding MongoDB port..."
        sudo ufw allow 27017
        echo "✅ Port 27017 allowed through UFW firewall"
    else
        echo "UFW firewall is not active"
    fi
    
    # Check if iptables has rules
    if sudo iptables -L | grep -q "Chain INPUT"; then
        echo "iptables rules detected. You may need to manually allow port 27017"
        echo "Example command: sudo iptables -A INPUT -p tcp --dport 27017 -j ACCEPT"
    fi
}

# Function to test MongoDB connection
test_mongodb() {
    echo ""
    echo "--- Testing MongoDB Connection ---"
    
    # Wait a moment for MongoDB to fully start
    sleep 3
    
    # Test local connection
    echo "Testing local connection..."
    if mongosh --eval "db.runCommand('ping')" --quiet > /dev/null 2>&1; then
        echo "✅ Local MongoDB connection successful"
    else
        echo "❌ Local MongoDB connection failed"
        return 1
    fi
    
    # Test if MongoDB is listening on the correct port
    if netstat -tlnp | grep :27017 > /dev/null 2>&1; then
        echo "✅ MongoDB is listening on port 27017"
    else
        echo "❌ MongoDB is not listening on port 27017"
        return 1
    fi
    
    echo ""
    echo "MongoDB is ready for connections!"
}

# Function to create a test database and user
setup_test_data() {
    echo ""
    echo "--- Setting up Test Database ---"
    
    # Create the shakeAndMatch database and a test user
    mongosh --eval "
        use shakeAndMatch;
        db.test.insertOne({message: 'MongoDB setup successful', timestamp: new Date()});
        print('✅ Test document inserted into shakeAndMatch database');
    " --quiet
    
    if [ $? -eq 0 ]; then
        echo "✅ Test database setup complete"
    else
        echo "❌ Failed to setup test database"
    fi
}

# Main execution
main() {
    echo "Starting MongoDB setup process..."
    echo ""
    
    # Check if MongoDB is already installed
    if ! check_mongodb_installed; then
        read -p "MongoDB is not installed. Would you like to install it? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            install_mongodb
        else
            echo "Installation cancelled."
            exit 0
        fi
    fi
    
    # Configure MongoDB
    read -p "Would you like to configure MongoDB for external connections? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        configure_mongodb
    fi
    
    # Start MongoDB service
    start_mongodb
    
    # Configure firewall
    configure_firewall
    
    # Test MongoDB
    test_mongodb
    
    # Setup test data
    setup_test_data
    
    echo ""
    echo "=== Setup Complete ==="
    echo ""
    echo "MongoDB should now be running and accessible."
    echo "You can test the connection using the diagnostic script:"
    echo "  node diagnose-mongodb.js"
    echo ""
    echo "To check MongoDB status: sudo systemctl status mongod"
    echo "To view MongoDB logs: sudo journalctl -u mongod -f"
    echo ""
    echo "⚠️  Security Note: MongoDB is now configured to accept external connections."
    echo "   Consider implementing authentication and restricting access in production."
}

# Run the main function
main
