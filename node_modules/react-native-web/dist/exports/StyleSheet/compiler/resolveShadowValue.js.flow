/**
 * Copyright (c) <PERSON>.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 */

import normalizeColor from './normalizeColor';
import normalizeValueWithProperty from './normalizeValueWithProperty';
const defaultOffset = {
  height: 0,
  width: 0
};
declare var resolveShadowValue: (style: Object) => void | string;
export default resolveShadowValue;