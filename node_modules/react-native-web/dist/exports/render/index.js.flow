/**
 * Copyright (c) <PERSON>.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @noflow
 */

'use client';

import { createRoot as domCreateRoot, hydrateRoot as domHydrateRoot } from 'react-dom/client';
import { createSheet } from '../StyleSheet/dom';
declare export function hydrate(element: any, root: any): any;
declare export default function render(element: any, root: any): any;