/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 */

import type { Touch, TouchEvent } from './ResponderEventTypes';
import { isStartish, isMoveish, isEndish } from './ResponderEventTypes';
type TouchRecord = {|
  currentPageX: number,
  currentPageY: number,
  currentTimeStamp: number,
  previousPageX: number,
  previousPageY: number,
  previousTimeStamp: number,
  startPageX: number,
  startPageY: number,
  startTimeStamp: number,
  touchActive: boolean,
|};
export type TouchHistory = $ReadOnly<{|
  indexOfSingleActiveTouch: number,
  mostRecentTimeStamp: number,
  numberActiveTouches: number,
  touchBank: Array<TouchRecord>,
|}>;

/**
 * Tracks the position and time of each active touch by `touch.identifier`. We
 * should typically only see IDs in the range of 1-20 because IDs get recycled
 * when touches end and start again.
 */

const __DEV__ = process.env.NODE_ENV !== 'production';
const MAX_TOUCH_BANK = 20;
declare function timestampForTouch(touch: Touch): number;
/**
 * TODO: Instead of making gestures recompute filtered velocity, we could
 * include a built in velocity computation that can be reused globally.
 */
declare function createTouchRecord(touch: Touch): TouchRecord;
declare function resetTouchRecord(touchRecord: TouchRecord, touch: Touch): void;
declare function getTouchIdentifier(arg0: Touch): number;
declare function recordTouchStart(touch: Touch, touchHistory: any): void;
declare function recordTouchMove(touch: Touch, touchHistory: any): void;
declare function recordTouchEnd(touch: Touch, touchHistory: any): void;
declare function printTouch(touch: Touch): string;
declare function printTouchBank(touchHistory: any): string;
declare export class ResponderTouchHistoryStore {
  _touchHistory: any,
  recordTouchTrack(topLevelType: string, nativeEvent: TouchEvent): void,
  touchHistory(): TouchHistory,
}